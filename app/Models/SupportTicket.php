<?php

namespace App\Models;

use App\Enums\TicketStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SupportTicket extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subject',
        'message',
        'status',
        'admin_response',
        'resolved_at',
    ];

    protected function casts(): array
    {
        return [
            'status' => TicketStatus::class,
            'resolved_at' => 'datetime',
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getIsResolvedAttribute(): bool
    {
        return in_array($this->status, [TicketStatus::RESOLVED, TicketStatus::CLOSED]);
    }

    public function getStatusColorAttribute(): string
    {
        return $this->status->color();
    }

    // Scopes
    public function scopeByStatus($query, TicketStatus $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOpen($query)
    {
        return $query->whereIn('status', [TicketStatus::OPEN, TicketStatus::IN_PROGRESS]);
    }

    public function scopeResolved($query)
    {
        return $query->whereIn('status', [TicketStatus::RESOLVED, TicketStatus::CLOSED]);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
