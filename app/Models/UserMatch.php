<?php

namespace App\Models;

use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserMatch extends Model
{
    use HasFactory;

    protected $table = 'matches';

    protected $fillable = [
        'user1_id',
        'user2_id',
        'category',
        'is_active',
        'matched_at',
    ];

    protected function casts(): array
    {
        return [
            'category' => CategoryType::class,
            'is_active' => 'boolean',
            'matched_at' => 'datetime',
        ];
    }

    // Relationships
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    // Helper method to get the other user in the match
    public function getOtherUser($currentUserId): ?User
    {
        if ($this->user1_id == $currentUserId) {
            return $this->user2;
        } elseif ($this->user2_id == $currentUserId) {
            return $this->user1;
        }

        return null;
    }

    // Accessors
    public function getMatchDurationAttribute(): string
    {
        return $this->matched_at->diffForHumans();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, CategoryType $category)
    {
        return $query->where('category', $category);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user1_id', $userId)
              ->orWhere('user2_id', $userId);
        });
    }

    public function scopeBetweenUsers($query, $user1Id, $user2Id)
    {
        return $query->where(function ($q) use ($user1Id, $user2Id) {
            $q->where(function ($subQ) use ($user1Id, $user2Id) {
                $subQ->where('user1_id', $user1Id)
                     ->where('user2_id', $user2Id);
            })->orWhere(function ($subQ) use ($user1Id, $user2Id) {
                $subQ->where('user1_id', $user2Id)
                     ->where('user2_id', $user1Id);
            });
        });
    }
}
