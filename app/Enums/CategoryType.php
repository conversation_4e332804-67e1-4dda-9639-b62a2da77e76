<?php

namespace App\Enums;

enum CategoryType: string
{
    case CASUAL = 'casual';
    case SERIOUS = 'serious';
    case MARRIAGE = 'marriage';
    case NETWORKING = 'networking';

    public function label(): string
    {
        return match($this) {
            self::CASUAL => 'Casual Dating',
            self::SERIOUS => 'Serious Relationship',
            self::MARRIAGE => 'Marriage',
            self::NETWORKING => 'Networking & Social Connections',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
