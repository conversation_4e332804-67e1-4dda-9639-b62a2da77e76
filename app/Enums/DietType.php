<?php

namespace App\Enums;

enum DietType: string
{
    case OMNIVORE = 'omnivore';
    case VEGETARIAN = 'vegetarian';
    case VEGAN = 'vegan';
    case PESCATARIAN = 'pescatarian';
    case KETO = 'keto';
    case PALEO = 'paleo';
    case GLUTEN_FREE = 'gluten_free';
    case OTHER = 'other';

    public function label(): string
    {
        return match($this) {
            self::OMNIVORE => 'Omnivore',
            self::VEGETARIAN => 'Vegetarian',
            self::VEGAN => 'Vegan',
            self::PESCATARIAN => 'Pescatarian',
            self::KETO => 'Keto',
            self::PALEO => 'Paleo',
            self::GLUTEN_FREE => 'Gluten Free',
            self::OTHER => 'Other',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
