<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('category', ['casual', 'serious', 'marriage', 'networking']);
            $table->text('description')->nullable();
            $table->timestamp('start_time');
            $table->timestamp('end_time');
            $table->string('location_name');
            $table->decimal('lat', 10, 8);
            $table->decimal('lng', 11, 8);
            $table->text('address');
            $table->string('qr_code_path')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('max_participants')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('category');
            $table->index('start_time');
            $table->index('end_time');
            $table->index(['lat', 'lng']);
            $table->index('is_active');
            $table->index(['category', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
