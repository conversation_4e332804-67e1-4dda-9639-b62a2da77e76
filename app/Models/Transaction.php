<?php

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\TransactionStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'category',
        'status',
        'stripe_txn_id',
        'amount',
        'expires_at',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'category' => CategoryType::class,
            'status' => TransactionStatus::class,
            'amount' => 'decimal:2',
            'expires_at' => 'datetime',
            'metadata' => 'array',
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->status === TransactionStatus::COMPLETED &&
               (!$this->expires_at || $this->expires_at->isFuture());
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    public function scopeActive($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    public function scopeByCategory($query, CategoryType $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByStatus($query, TransactionStatus $status)
    {
        return $query->where('status', $status);
    }
}
