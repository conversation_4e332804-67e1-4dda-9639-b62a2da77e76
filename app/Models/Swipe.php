<?php

namespace App\Models;

use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Swipe extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'swiped_user_id',
        'is_like',
        'category',
    ];

    protected function casts(): array
    {
        return [
            'is_like' => 'boolean',
            'category' => CategoryType::class,
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function swipedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'swiped_user_id');
    }

    // Accessors
    public function getSwipeTypeAttribute(): string
    {
        return $this->is_like ? 'like' : 'dislike';
    }

    // Scopes
    public function scopeLikes($query)
    {
        return $query->where('is_like', true);
    }

    public function scopeDislikes($query)
    {
        return $query->where('is_like', false);
    }

    public function scopeByCategory($query, CategoryType $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForUser($query, $swipedUserId)
    {
        return $query->where('swiped_user_id', $swipedUserId);
    }
}
