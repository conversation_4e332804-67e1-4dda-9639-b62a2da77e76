<?php

namespace App\Enums;

enum EducationLevel: string
{
    case HIGH_SCHOOL = 'high_school';
    case SOME_COLLEGE = 'some_college';
    case BACHELORS = 'bachelors';
    case MASTERS = 'masters';
    case PHD = 'phd';
    case TRADE_SCHOOL = 'trade_school';
    case OTHER = 'other';

    public function label(): string
    {
        return match($this) {
            self::HIGH_SCHOOL => 'High School',
            self::SOME_COLLEGE => 'Some College',
            self::BACHELORS => 'Bachelor\'s Degree',
            self::MASTERS => 'Master\'s Degree',
            self::PHD => 'PhD',
            self::TRADE_SCHOOL => 'Trade School',
            self::OTHER => 'Other',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
