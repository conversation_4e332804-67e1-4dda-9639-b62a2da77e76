<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class UserImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'image_path',
        'is_profile_image',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'is_profile_image' => 'boolean',
            'order' => 'integer',
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getImageUrlAttribute(): string
    {
        return Storage::url($this->image_path);
    }

    public function getFullImageUrlAttribute(): string
    {
        return url(Storage::url($this->image_path));
    }

    // Scopes
    public function scopeProfileImages($query)
    {
        return $query->where('is_profile_image', true);
    }

    public function scopeOrderedImages($query)
    {
        return $query->orderBy('order');
    }
}
