<?php

namespace App\Models;

use App\Enums\GenderType;
use App\Enums\EducationLevel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'dob',
        'gender',
        'looking_for',
        'location',
        'lat',
        'lng',
        'work_at',
        'religion',
        'ethnicity',
        'education_level',
        'preferred_age_range',
        'preferred_distance',
        'bio',
        'is_profile_complete',
    ];

    protected function casts(): array
    {
        return [
            'dob' => 'date',
            'gender' => GenderType::class,
            'lat' => 'decimal:8',
            'lng' => 'decimal:8',
            'education_level' => EducationLevel::class,
            'preferred_age_range' => 'array',
            'preferred_distance' => 'integer',
            'is_profile_complete' => 'boolean',
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getAgeAttribute(): ?int
    {
        return $this->dob ? $this->dob->age : null;
    }

    public function getDistanceFromAttribute(): \Closure
    {
        return function ($lat, $lng) {
            if (!$this->lat || !$this->lng) {
                return null;
            }

            $earthRadius = 6371; // Earth's radius in kilometers

            $latDelta = deg2rad($lat - $this->lat);
            $lngDelta = deg2rad($lng - $this->lng);

            $a = sin($latDelta / 2) * sin($latDelta / 2) +
                cos(deg2rad($this->lat)) * cos(deg2rad($lat)) *
                sin($lngDelta / 2) * sin($lngDelta / 2);

            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

            return round($earthRadius * $c, 2);
        };
    }

    // Scopes
    public function scopeComplete($query)
    {
        return $query->where('is_profile_complete', true);
    }

    public function scopeWithinDistance($query, $lat, $lng, $distance = 50)
    {
        return $query->whereRaw(
            "(6371 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) <= ?",
            [$lat, $lng, $lat, $distance]
        );
    }

    public function scopeByGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    public function scopeByAgeRange($query, $minAge, $maxAge)
    {
        $minDate = now()->subYears($maxAge)->format('Y-m-d');
        $maxDate = now()->subYears($minAge)->format('Y-m-d');

        return $query->whereBetween('dob', [$minDate, $maxDate]);
    }
}
