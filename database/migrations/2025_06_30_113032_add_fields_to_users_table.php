<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('otp')->nullable()->after('phone');
            $table->boolean('is_verified')->default(false)->after('otp');
            $table->string('password')->nullable()->change();

            // Add indexes for performance
            $table->index('phone');
            $table->index('is_verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['phone']);
            $table->dropIndex(['is_verified']);
            $table->dropColumn(['phone', 'otp', 'is_verified']);
            $table->string('password')->nullable(false)->change();
        });
    }
};
