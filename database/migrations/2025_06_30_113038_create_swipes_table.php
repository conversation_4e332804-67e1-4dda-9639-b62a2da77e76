<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('swipes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('swiped_user_id')->constrained('users')->onDelete('cascade');
            $table->boolean('is_like')->default(false); // true for like, false for dislike
            $table->enum('category', ['casual', 'serious', 'marriage', 'networking']);
            $table->timestamps();

            // Unique constraint to prevent duplicate swipes
            $table->unique(['user_id', 'swiped_user_id', 'category']);

            // Indexes for performance
            $table->index('user_id');
            $table->index('swiped_user_id');
            $table->index('is_like');
            $table->index('category');
            $table->index(['user_id', 'is_like']);
            $table->index(['swiped_user_id', 'is_like']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('swipes');
    }
};
