<?php

namespace App\Enums;

enum DrinkingStatus: string
{
    case NEVER = 'never';
    case OCCASIONALLY = 'occasionally';
    case SOCIALLY = 'socially';
    case REGULARLY = 'regularly';

    public function label(): string
    {
        return match($this) {
            self::NEVER => 'Never',
            self::OCCASIONALLY => 'Occasionally',
            self::SOCIALLY => 'Socially',
            self::REGULARLY => 'Regularly',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
