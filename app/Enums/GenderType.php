<?php

namespace App\Enums;

enum GenderType: string
{
    case MALE = 'male';
    case FEMALE = 'female';
    case NON_BINARY = 'non_binary';
    case PREFER_NOT_TO_SAY = 'prefer_not_to_say';

    public function label(): string
    {
        return match($this) {
            self::MALE => 'Male',
            self::FEMALE => 'Female',
            self::NON_BINARY => 'Non-Binary',
            self::PREFER_NOT_TO_SAY => 'Prefer not to say',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
