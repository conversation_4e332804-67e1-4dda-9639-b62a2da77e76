<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('matches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user1_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('user2_id')->constrained('users')->onDelete('cascade');
            $table->enum('category', ['casual', 'serious', 'marriage', 'networking']);
            $table->boolean('is_active')->default(true);
            $table->timestamp('matched_at')->useCurrent();
            $table->timestamps();

            // Unique constraint to prevent duplicate matches
            $table->unique(['user1_id', 'user2_id', 'category']);

            // Indexes for performance
            $table->index('user1_id');
            $table->index('user2_id');
            $table->index('category');
            $table->index('is_active');
            $table->index('matched_at');
            $table->index(['user1_id', 'is_active']);
            $table->index(['user2_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('matches');
    }
};
