<?php

namespace App\Models;

use App\Enums\SmokingStatus;
use App\Enums\DrinkingStatus;
use App\Enums\DietType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'smokes',
        'drinks',
        'diet_type',
    ];

    protected function casts(): array
    {
        return [
            'smokes' => SmokingStatus::class,
            'drinks' => DrinkingStatus::class,
            'diet_type' => DietType::class,
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
