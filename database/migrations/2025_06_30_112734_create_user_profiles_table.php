<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('dob')->nullable();
            $table->enum('gender', ['male', 'female', 'non_binary', 'prefer_not_to_say'])->nullable();
            $table->enum('looking_for', ['male', 'female', 'non_binary', 'everyone'])->nullable();
            $table->string('location')->nullable();
            $table->decimal('lat', 10, 8)->nullable();
            $table->decimal('lng', 11, 8)->nullable();
            $table->string('work_at')->nullable();
            $table->string('religion')->nullable();
            $table->string('ethnicity')->nullable();
            $table->enum('education_level', ['high_school', 'some_college', 'bachelors', 'masters', 'phd', 'trade_school', 'other'])->nullable();
            $table->json('preferred_age_range')->nullable(); // {min: 18, max: 65}
            $table->integer('preferred_distance')->default(50); // in kilometers
            $table->text('bio')->nullable();
            $table->boolean('is_profile_complete')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('gender');
            $table->index('looking_for');
            $table->index(['lat', 'lng']);
            $table->index('is_profile_complete');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profiles');
    }
};
