<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('smokes', ['never', 'occasionally', 'regularly', 'trying_to_quit'])->nullable();
            $table->enum('drinks', ['never', 'occasionally', 'socially', 'regularly'])->nullable();
            $table->enum('diet_type', ['omnivore', 'vegetarian', 'vegan', 'pescatarian', 'keto', 'paleo', 'gluten_free', 'other'])->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('smokes');
            $table->index('drinks');
            $table->index('diet_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
