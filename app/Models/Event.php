<?php

namespace App\Models;

use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'description',
        'start_time',
        'end_time',
        'location_name',
        'lat',
        'lng',
        'address',
        'qr_code_path',
        'is_active',
        'max_participants',
    ];

    protected function casts(): array
    {
        return [
            'category' => CategoryType::class,
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'lat' => 'decimal:8',
            'lng' => 'decimal:8',
            'is_active' => 'boolean',
            'max_participants' => 'integer',
        ];
    }

    // Accessors
    public function getQrCodeUrlAttribute(): ?string
    {
        return $this->qr_code_path ? Storage::url($this->qr_code_path) : null;
    }

    public function getFullQrCodeUrlAttribute(): ?string
    {
        return $this->qr_code_path ? url(Storage::url($this->qr_code_path)) : null;
    }

    public function getIsOngoingAttribute(): bool
    {
        return now()->between($this->start_time, $this->end_time);
    }

    public function getIsUpcomingAttribute(): bool
    {
        return $this->start_time->isFuture();
    }

    public function getIsPastAttribute(): bool
    {
        return $this->end_time->isPast();
    }

    public function getDurationInHoursAttribute(): float
    {
        return $this->start_time->diffInHours($this->end_time);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, CategoryType $category)
    {
        return $query->where('category', $category);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now());
    }

    public function scopeOngoing($query)
    {
        return $query->where('start_time', '<=', now())
                    ->where('end_time', '>=', now());
    }

    public function scopePast($query)
    {
        return $query->where('end_time', '<', now());
    }

    public function scopeWithinDistance($query, $lat, $lng, $distance = 50)
    {
        return $query->whereRaw(
            "(6371 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) <= ?",
            [$lat, $lng, $lat, $distance]
        );
    }
}
