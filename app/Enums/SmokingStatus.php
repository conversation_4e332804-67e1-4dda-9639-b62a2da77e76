<?php

namespace App\Enums;

enum SmokingStatus: string
{
    case NEVER = 'never';
    case OCCASIONALLY = 'occasionally';
    case REGULARLY = 'regularly';
    case TRYING_TO_QUIT = 'trying_to_quit';

    public function label(): string
    {
        return match($this) {
            self::NEVER => 'Never',
            self::OCCASIONALLY => 'Occasionally',
            self::REGULARLY => 'Regularly',
            self::TRYING_TO_QUIT => 'Trying to quit',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label()
        ], self::cases());
    }
}
